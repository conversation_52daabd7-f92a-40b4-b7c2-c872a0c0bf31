/**
 * Componente de seleção em massa para a tela de atribuir grupos
 * Implementação em JavaScript puro inspirada no BulkSelectionFooter.vue
 */

window.BulkSelectionFooter = {
  // Configurações
  config: {
    footerId: "bulk-selection-footer",
    containerClass: "bulk-selection-footer",
    selectedItems: [],
    allItemsData: [], // Armazenar dados de todos os itens para seleção em massa
    totalItems: 0,
    isLoadingSelectAll: false,
    hasSelectedAll: false,
    maxSelectableItems: 1000, // Limite máximo de itens selecionáveis
  },

  /**
   * Inicializar o componente
   */
  init: function () {
    this.createFooterElement();
    this.updateTotalItems();
    this.setupCheckboxListeners();
    this.setupMasterCheckbox();
    this.updateDisplay();
  },

  /**
   * Criar elemento do footer
   */
  createFooterElement: function () {
    // Remover footer existente se houver
    const existingFooter = document.getElementById(this.config.footerId);
    if (existingFooter) {
      existingFooter.remove();
    }

    // Criar novo footer
    const footer = document.createElement("div");
    footer.id = this.config.footerId;
    footer.className = this.config.containerClass;
    footer.style.cssText = `
      position: fixed;
      bottom: 0px;
      left: 50%;
      transform: translateX(-50%);
      background-color: #3276b1;
      color: white;
      padding: 10px 25px;
      z-index: 1000;
      box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.15);
      border-radius: 6px 6px 0 0;
      white-space: nowrap;
      transition: bottom 0.2s;
      display: none;
    `;

    footer.innerHTML = `
      <div class="footer-content" style="display: flex; align-items: center; gap: 20px; font-size: 14px;">
        <span class="selection-summary" style="font-weight: 500;">
          Você selecionou: <span id="selected-count" style="font-weight: 600;">0</span> item(ns).
        </span>
        <div class="footer-actions" style="display: flex; align-items: center; gap: 12px;">
          <a href="#" id="select-all-link" class="footer-action-link" style="color: white; text-decoration: underline; cursor: pointer; font-weight: 600; font-size: 14px;">
            Selecionar todos os <span id="total-count">0</span> item(ns) <span style="font-size: 12px; opacity: 0.8;">(máx. 1000)</span>
          </a>
          <span class="separator" style="color: #e9ecef; font-weight: 300;">|</span>
          <a href="#" id="clear-selection-link" class="footer-action-link" style="color: white; text-decoration: underline; cursor: pointer; font-weight:600; font-size: 14px;">
            Limpar seleção
          </a>
        </div>
      </div>
    `;

    document.body.appendChild(footer);
    this.setupFooterListeners();
  },

  /**
   * Configurar listeners do footer
   */
  setupFooterListeners: function () {
    const self = this;

    // Selecionar todos
    document
      .getElementById("select-all-link")
      .addEventListener("click", function (e) {
        e.preventDefault();
        self.handleSelectAll();
      });

    // Limpar seleção
    document
      .getElementById("clear-selection-link")
      .addEventListener("click", function (e) {
        e.preventDefault();
        self.handleClearSelection();
      });

    // Atribuir selecionados
    // document
    //   .getElementById("atribuir-selected-link")
    //   .addEventListener("click", function (e) {
    //     e.preventDefault();
    //     self.handleAtribuir();
    //   });
  },
  /**
   * Atualizar total de itens
   */
  updateTotalItems: function () {
    // Buscar total de itens do badge de resultado
    const badge = document.querySelector(".result-container-badge");
    if (badge) {
      this.config.totalItems = parseInt(badge.textContent) || 0;
    }

    // Fallback: contar checkboxes na página
    if (this.config.totalItems === 0) {
      this.config.totalItems = document.querySelectorAll(
        'input[name="item[]"]'
      ).length;
    }

    // Atualizar display
    const totalCountSpan = document.getElementById("total-count");
    if (totalCountSpan) {
      totalCountSpan.textContent = this.config.totalItems;
    }
  },

  /**
   * Configurar listeners para checkboxes
   */
  setupCheckboxListeners: function () {
    const self = this;

    // Listener para mudanças nos checkboxes individuais
    $(document).on("change", 'input[name="item[]"]', function () {
      self.updateSelectedItems();
      self.updateDisplay();
      self.updateMasterCheckboxState();
    });
  },

  /**
   * Configurar checkbox master (selecionar todos)
   */
  setupMasterCheckbox: function () {
    const self = this;
    const masterCheckbox = document.getElementById("select-all-checkbox");

    if (masterCheckbox) {
      masterCheckbox.addEventListener("change", function () {
        if (this.checked) {
          self.handleSelectAll();
        } else {
          self.handleClearSelection();
        }
      });
    }
  },

  /**
   * Atualizar estado do checkbox master baseado na seleção atual
   */
  updateMasterCheckboxState: function () {
    const masterCheckbox = document.getElementById("select-all-checkbox");
    if (!masterCheckbox) return;

    const allCheckboxes = document.querySelectorAll(
      'input[name="item[]"]:not(:disabled)'
    );
    const checkedCheckboxes = document.querySelectorAll(
      'input[name="item[]"]:checked'
    );

    if (checkedCheckboxes.length === 0) {
      // Nenhum selecionado
      masterCheckbox.checked = false;
      masterCheckbox.indeterminate = false;
    } else if (checkedCheckboxes.length === allCheckboxes.length) {
      // Todos selecionados
      masterCheckbox.checked = true;
      masterCheckbox.indeterminate = false;
    } else {
      // Alguns selecionados (estado indeterminado)
      masterCheckbox.checked = false;
      masterCheckbox.indeterminate = true;
    }
  },

  /**
   * Atualizar lista de itens selecionados
   */
  updateSelectedItems: function () {
    const checkboxes = document.querySelectorAll(
      'input[name="item[]"]:checked'
    );
    this.config.selectedItems = Array.from(checkboxes).map((checkbox) => ({
      part_number: checkbox.value,
      estabelecimento: checkbox.dataset.estabelecimento,
      tag: checkbox.dataset.tag,
      descricao: checkbox.dataset.descricao,
      ncm: checkbox.dataset.ncmItem,
    }));
  },

  /**
   * Atualizar display do footer
   */
  updateDisplay: function () {
    const footer = document.getElementById(this.config.footerId);
    const selectedCountSpan = document.getElementById("selected-count");

    if (footer && selectedCountSpan) {
      selectedCountSpan.textContent = this.config.selectedItems.length;

      // Mostrar/ocultar footer baseado na seleção
      if (this.config.selectedItems.length > 0) {
        footer.style.display = "block";
      } else {
        footer.style.display = "none";
      }
    }
  },

  /**
   * Selecionar todos os itens (até 1000)
   */
  handleSelectAll: function () {
    if (this.config.isLoadingSelectAll) return;

    this.config.isLoadingSelectAll = true;

    // Mostrar loading no link
    const selectAllLink = document.getElementById("select-all-link");
    if (selectAllLink) {
      selectAllLink.innerHTML =
        '<i class="fa fa-spinner fa-spin"></i> Carregando...';
    }

    // Fazer requisição AJAX para buscar todos os itens
    this.fetchAllItemsForBulkSelection()
      .then((response) => {
        if (response.status === 200 && response.data) {
          // Armazenar dados de todos os itens
          this.config.allItemsData = response.data;

          // Selecionar todos os checkboxes visíveis na página atual
          const allCheckboxes = document.querySelectorAll(
            'input[name="item[]"]:not(:disabled)'
          );
          allCheckboxes.forEach((checkbox) => {
            checkbox.checked = true;
          });

          // Atualizar lista de itens selecionados com todos os dados
          this.updateSelectedItemsWithAllData();
          this.updateDisplay();
          this.updateMasterCheckboxState();
          this.config.hasSelectedAll = true;

          // Mostrar mensagem de sucesso
          if (response.total > 50) {
            this.showNotification(
              `${response.total} itens selecionados (incluindo itens de outras páginas)`,
              "success"
            );
          }
        } else {
          this.showNotification(
            "Erro ao carregar todos os itens: " +
              (response.msg || "Erro desconhecido"),
            "error"
          );
          // Fallback: selecionar apenas os visíveis
          this.selectOnlyVisibleItems();
        }
      })
      .catch((error) => {
        console.error("Erro ao buscar todos os itens:", error);
        this.showNotification(
          "Erro ao carregar todos os itens. Selecionando apenas os visíveis.",
          "warning"
        );
        // Fallback: selecionar apenas os visíveis
        this.selectOnlyVisibleItems();
      })
      .finally(() => {
        this.config.isLoadingSelectAll = false;
        // Restaurar texto do link
        if (selectAllLink) {
          selectAllLink.innerHTML = `Selecionar todos os <span id="total-count">${this.config.totalItems}</span> item(ns) <span style="font-size: 12px; opacity: 0.8;">(máx. 1000)</span>`;
        }
      });
  },

  /**
   * Fallback para selecionar apenas itens visíveis
   */
  selectOnlyVisibleItems: function () {
    const allCheckboxes = document.querySelectorAll(
      'input[name="item[]"]:not(:disabled)'
    );
    allCheckboxes.forEach((checkbox) => {
      checkbox.checked = true;
    });

    this.updateSelectedItems();
    this.updateDisplay();
    this.updateMasterCheckboxState();
    this.config.hasSelectedAll = false; // Não selecionou todos, apenas os visíveis
  },

  /**
   * Buscar todos os itens para seleção em massa via AJAX
   */
  fetchAllItemsForBulkSelection: function () {
    return new Promise((resolve, reject) => {
      $.ajax({
        url: base_url + "atribuir_grupo/ajax_get_all_items_for_bulk_selection",
        type: "POST",
        dataType: "json",
        timeout: 30000, // 30 segundos de timeout
        success: function (response) {
          resolve(response);
        },
        error: function (xhr, status, error) {
          reject(error);
        },
      });
    });
  },

  /**
   * Atualizar lista de itens selecionados incluindo dados de todos os itens
   */
  updateSelectedItemsWithAllData: function () {
    // Começar com os itens visíveis selecionados
    this.updateSelectedItems();

    // Se temos dados de todos os itens, usar esses dados
    if (this.config.allItemsData && this.config.allItemsData.length > 0) {
      this.config.selectedItems = this.config.allItemsData.map((item) => ({
        part_number: item.part_number,
        estabelecimento: item.estabelecimento,
        tag: item.tag,
        descricao: item.descricao,
        ncm: item.ncm,
      }));
    }
  },

  /**
   * Mostrar notificação para o usuário
   */
  showNotification: function (message, type = "info") {
    // Usar SweetAlert se disponível
    if (typeof swal !== "undefined") {
      const title =
        type === "success"
          ? "Sucesso!"
          : type === "error"
          ? "Erro!"
          : type === "warning"
          ? "Atenção!"
          : "Informação";
      swal(title, message, type);
    } else {
      // Fallback para alert simples
      alert(message);
    }
  },

  /**
   * Limpar seleção
   */
  handleClearSelection: function () {
    const allCheckboxes = document.querySelectorAll('input[name="item[]"]');
    allCheckboxes.forEach((checkbox) => {
      checkbox.checked = false;
    });

    this.config.selectedItems = [];
    this.config.allItemsData = []; // Limpar também os dados de todos os itens
    this.config.hasSelectedAll = false;
    this.updateDisplay();
    this.updateMasterCheckboxState();
  },

  /**
   * Obter todos os itens selecionados (incluindo os de outras páginas)
   */
  getAllSelectedItems: function () {
    return this.config.selectedItems;
  },

  /**
   * Verificar se todos os itens foram selecionados
   */
  hasSelectedAllItems: function () {
    return this.config.hasSelectedAll;
  },

  /**
   * Obter contagem de itens selecionados
   */
  getSelectedItemsCount: function () {
    return this.config.selectedItems.length;
  },

  /**
   * Atribuir itens selecionados
   */
  handleAtribuir: function () {
    if (this.config.selectedItems.length === 0) {
      return;
    }

    // Usar a funcionalidade existente do botão "Atribuir"
    const atribuirBtn = document.querySelector(".btn.btn-primary.actions");
    if (atribuirBtn) {
      atribuirBtn.click();
    }
  },
};
