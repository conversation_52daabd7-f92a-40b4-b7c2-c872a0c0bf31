<template>
    <div>
        <loading :active.sync="isLoading" :is-full-page="fullPage">
        </loading>

        <div class="modal fade" id="editItem" tabindex="-1" role="dialog" aria-labelledby="editItemLabel">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="editItemLabel">Editar itens</h4>
                    </div>

                    <div class="modal-body">
                        <div class="alert alert-danger alert-dismissible" role="alert" v-if="alert">
                            <strong>Atenção!</strong> Preencha um dos campos para continuar.
                        </div>

                        <div class="form-group">
                            <label for="partnumbers">Partnumbers</label>
                            <v-select v-model="selectedPartnumbers" placeholder="Partnumbers" :multiple="true"
                                :options="items">
                                <div slot="no-options" name="no-options">Não encontramos nenhuma opção.</div>
                            </v-select>
                        </div>

                        <div class="form-group" v-if="hasPeso">
                            <label for="peso">Peso</label>
                            <input type="text" v-model="peso" id="peso" class="form-control">
                        </div>

                        <div class="form-group" v-if="hasPrioridade">
                            <label for="prioridade">Prioridade</label>
                            <v-select v-model="prioridade" placeholder="Prioridade" :multiple="false"
                                :options="empresa_prioridades" :disabled="!perCriticidade">
                                <div slot="no-options" name="no-options">Não encontramos nenhuma opção.</div>
                            </v-select>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">Cancelar</button>
                        <button type="button" @click="updateItems" class="btn btn-primary">Salvar</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import 'vue-loading-overlay/dist/vue-loading.css';
import Loading from 'vue-loading-overlay';

import _ from 'lodash';
import vSelect from 'vue-select';

export default {
    components: {
        Loading, vSelect
    },
    data() {
        return {
            items: [],
            selectedPartnumbers: [],
            selectedPartnumbersWithEstabelecimento: [],
            prioridade: '',
            peso: '',
            isLoading: false,
            fullPage: true,
            alert: false,
            empresa_prioridades: []
        }
    },
    props: {
        hasPrioridade: {
            required: true,
            type: String | Boolean
        },
        hasPeso: {
            required: true,
            type: String | Boolean
        },
        perCriticidade: {
            required: true,
            type: String | Boolean
        }
    },
    methods: {
        updateItems() {
            if (_.isEmpty(this.prioridade) && _.isEmpty(this.peso)) {
                this.alert = true;

                return;
            }

            this.isLoading = true;

            this.selectedPartnumbersWithEstabelecimento = this.selectedPartnumbersWithEstabelecimento.filter(item => {
                return this.selectedPartnumbers.find(value => value == item.partnumber);
            });

            this.$http.post('atribuir_grupo/ajax_update_items/', {
                items: this.selectedPartnumbersWithEstabelecimento,
                prioridade: this.prioridade,
                peso: this.peso
            }).then(({ data }) => {
                atribuir_grupos.get_itens();
                this.isLoading = false;

                let title = data.status == 200 ? "Sucesso!" : "Erro!";
                let type = data.status == 200 ? "success" : "error";

                swal(title, data.message, type);
            });

            $("#editItem").modal("hide");
        }
    },
    mounted() {
        let self = this;

        $("#editItem").on("show.bs.modal", function () {
            self.selectedPartnumbersWithEstabelecimento = [];
            self.selectedPartnumbers = [];
            self.items = [];

            // Verificar se o BulkSelectionFooter tem itens selecionados
            if (window.BulkSelectionFooter && window.BulkSelectionFooter.hasSelectedAllItems()) {
                // Usar dados do BulkSelectionFooter se todos os itens foram selecionados
                const allSelectedItems = window.BulkSelectionFooter.getAllSelectedItems();

                allSelectedItems.forEach(function (item) {
                    self.items.push(item.part_number);
                    self.selectedPartnumbers.push(item.part_number);
                    self.selectedPartnumbersWithEstabelecimento.push({
                        partnumber: item.part_number,
                        estabelecimento: item.estabelecimento
                    });
                });

                console.log(`EditItem: Carregados ${allSelectedItems.length} itens do BulkSelectionFooter`);
            } else {
                // Usar apenas os checkboxes visíveis (comportamento original)
                $('.item_selected:checked').each(function (data) {
                    self.items.push($(this).val());
                    self.selectedPartnumbers.push($(this).val());
                    self.selectedPartnumbersWithEstabelecimento.push({
                        partnumber: $(this).val(),
                        estabelecimento: $(this).data('estabelecimento')
                    });
                });
            }

            self.$http.get('atribuir_grupo/ajax_get_prioridades')
                .then(response => {
                    self.empresa_prioridades = response.data.map((item) => {
                        return {
                            key: item.id_prioridade,
                            label: item.nome,
                            item: item
                        };
                    })
                })
                .catch(error => {
                    console.error('Erro ao obter prioridades:', error);
                });
        });
    }
}
</script>