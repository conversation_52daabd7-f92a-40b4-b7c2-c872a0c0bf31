# Funcionalidade de Seleção em Massa - Dados Técnicos

## Visão Geral

Esta funcionalidade permite aos usuários selecionar até 1000 itens de uma só vez, mesmo quando os dados estão paginados (50 itens por página). Quando o usuário clica em "Selecionar Todos", o sistema busca todos os itens que correspondem aos filtros atuais e os disponibiliza para os componentes que dependem da seleção.

## Arquivos Modificados

### 1. Controller: `application/controllers/atribuir_grupo.php`

**Novo método adicionado:**
- `ajax_get_all_items_for_bulk_selection()`: Endpoint que retorna até 1000 itens baseado nos filtros da sessão atual

**Funcionalidades:**
- Respeita as mesmas permissões e filtros da consulta paginada
- Aplica filtros de SLA e outros filtros ativos
- Retorna apenas itens não bloqueados para o usuário atual
- Limite máximo de 1000 itens por questões de performance

### 2. JavaScript: `assets/js/atribuir_grupos/bulk_selection_footer.js`

**Novas funcionalidades:**
- `fetchAllItemsForBulkSelection()`: Faz requisição AJAX para buscar todos os itens
- `updateSelectedItemsWithAllData()`: Atualiza a lista de selecionados com dados completos
- `getAllSelectedItems()`: Retorna todos os itens selecionados (incluindo de outras páginas)
- `hasSelectedAllItems()`: Verifica se a seleção em massa foi ativada
- `showNotification()`: Exibe notificações para o usuário

**Melhorias:**
- Loading indicator durante a busca
- Fallback para seleção apenas dos itens visíveis em caso de erro
- Notificações informativas sobre o processo
- Armazenamento dos dados completos dos itens selecionados

### 3. Vue.js: `assets/vuejs/src/components/GrupoTarifario/EditItem.vue`

**Modificações:**
- Verifica se `BulkSelectionFooter.hasSelectedAllItems()` é verdadeiro
- Se sim, usa `BulkSelectionFooter.getAllSelectedItems()` para obter todos os itens
- Se não, mantém comportamento original (apenas checkboxes visíveis)

### 4. Vue.js: `assets/vuejs/src/components/PerguntasRespostas/ModalPergunta.vue`

**Modificações:**
- Mesma lógica do EditItem.vue
- Prioriza dados do BulkSelectionFooter quando disponíveis
- Fallback para comportamento original

## Como Funciona

### Fluxo Normal (Sem Seleção em Massa)
1. Usuário marca checkboxes individuais
2. Componentes Vue.js leem apenas os checkboxes visíveis
3. Máximo de 50 itens por página

### Fluxo com Seleção em Massa
1. Usuário clica em "Selecionar Todos"
2. `BulkSelectionFooter` faz requisição AJAX para `ajax_get_all_items_for_bulk_selection`
3. Controller retorna até 1000 itens baseado nos filtros atuais
4. `BulkSelectionFooter` armazena os dados e marca checkboxes visíveis
5. Componentes Vue.js detectam `hasSelectedAllItems()` = true
6. Componentes usam `getAllSelectedItems()` em vez dos checkboxes visíveis
7. Usuário pode trabalhar com até 1000 itens de uma vez

## Limitações e Considerações

### Performance
- Limite de 1000 itens para evitar problemas de performance
- Timeout de 30 segundos na requisição AJAX
- Dados são carregados apenas quando necessário

### Compatibilidade
- Mantém compatibilidade total com o comportamento anterior
- Fallback automático em caso de erro
- Não quebra funcionalidades existentes

### Segurança
- Respeita todas as permissões existentes
- Aplica mesmos filtros de owner e estabelecimento
- Não expõe itens bloqueados para o usuário

## Interface do Usuário

### Indicadores Visuais
- Texto "(máx. 1000)" no link de seleção
- Loading spinner durante a busca
- Notificação informando quantos itens foram selecionados
- Mensagem específica quando inclui itens de outras páginas

### Mensagens
- **Sucesso**: "X itens selecionados (incluindo itens de outras páginas)"
- **Erro**: "Erro ao carregar todos os itens: [detalhes]"
- **Fallback**: "Erro ao carregar todos os itens. Selecionando apenas os visíveis."

## Testes Recomendados

### Cenários de Teste
1. **Seleção normal**: Marcar alguns checkboxes e verificar componentes
2. **Seleção em massa**: Clicar "Selecionar Todos" e verificar se carrega mais de 50 itens
3. **Filtros ativos**: Aplicar filtros e verificar se seleção respeita os filtros
4. **Permissões**: Testar com usuários com diferentes permissões
5. **Erro de rede**: Simular erro na requisição AJAX
6. **Performance**: Testar com consultas que retornam próximo de 1000 itens

### Verificações
- [ ] Componente EditItem recebe todos os itens selecionados
- [ ] Componente ModalPergunta recebe todos os itens selecionados
- [ ] Notificações são exibidas corretamente
- [ ] Fallback funciona em caso de erro
- [ ] Performance é aceitável com 1000 itens
- [ ] Permissões são respeitadas
- [ ] Filtros são aplicados corretamente

## Manutenção Futura

### Pontos de Atenção
- Monitorar performance com grandes volumes de dados
- Considerar aumentar limite se necessário (com cuidado)
- Verificar se novos componentes precisam da mesma integração
- Manter compatibilidade ao modificar estrutura de dados

### Possíveis Melhorias
- Cache dos resultados para evitar requisições repetidas
- Paginação inteligente para volumes muito grandes
- Indicador de progresso mais detalhado
- Persistência da seleção entre navegações
